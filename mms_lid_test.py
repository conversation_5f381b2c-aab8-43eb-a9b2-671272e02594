import os
import json
import soundfile as sf
import librosa
import numpy as np
import torch
from transformers import Wav2Vec2ForSequenceClassification, AutoFeatureExtractor
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from tqdm import tqdm
import argparse
import logging
from collections import Counter

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

# Setup logging
file_handler = logging.FileHandler("lid_test.log", mode="a", encoding="utf-8")
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[file_handler])
logger = logging.getLogger(__name__)

class MMSLIDTester:
    def __init__(self, model_path="facebook/mms-lid-512", max_workers=2, batch_size=8, device=None):
        self.model_path = model_path
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.lock = Lock()
        
        # Setup device
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        logger.info(f"Using device: {self.device}")
        
        # Load model and processor
        logger.info(f"Loading MMS LID model from {model_path}...")
        self.processor = AutoFeatureExtractor.from_pretrained(model_path)
        self.model = Wav2Vec2ForSequenceClassification.from_pretrained(model_path)
        
        # Move model to device
        self.model = self.model.to(self.device)
        self.model.eval()  # Set to evaluation mode
        
        logger.info("LID Model loaded successfully!")
        logger.info(f"Model supports {len(self.model.config.id2label)} languages")


    def load_audio_file(self, file_path, target_sr=16000):
        """Load and resample audio file"""
        try:
            data, samplerate = sf.read(file_path)
            if samplerate != target_sr:
                data = librosa.resample(data, orig_sr=samplerate, target_sr=target_sr)
            return np.asarray(data, dtype=np.float32)
        except Exception as e:
            logger.error(f"Error loading audio file {file_path}: {e}")
            return None
    
    def load_audio_batch(self, audio_paths):
        """Load a batch of audio files"""
        audio_data = []
        valid_paths = []
        
        for path in audio_paths:
            data = self.load_audio_file(path)
            if data is not None:
                audio_data.append(data)
                valid_paths.append(path)
        
        return audio_data, valid_paths
    
    def infer_batch(self, audio_batch, audio_paths):
        """Perform batch LID inference on multiple audio files"""
        try:
            if not audio_batch:
                return [None] * len(audio_paths)
            
            # Process batch with padding
            inputs = self.processor(
                audio_batch, 
                sampling_rate=16000, 
                return_tensors="pt", 
                padding=True
            )
            
            # Move inputs to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Inference
            with torch.no_grad():
                outputs = self.model(**inputs).logits
            
            # Decode results
            predictions = []
            for i in range(outputs.shape[0]):
                lang_id = torch.argmax(outputs[i], dim=-1).item()
                detected_lang = self.model.config.id2label[lang_id]
                # Also get confidence score
                confidence = torch.softmax(outputs[i], dim=-1).max().item()
                predictions.append({
                    'language': detected_lang,
                    'confidence': confidence,
                    'lang_id': lang_id
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error during batch LID inference: {e}")
            return [None] * len(audio_paths)
    
    def process_batch(self, test_items):
        """Process a batch of test items with GPU batch inference"""
        results = []
        
        # Group items into inference batches
        for i in range(0, len(test_items), self.batch_size):
            batch_items = test_items[i:i + self.batch_size]
            
            # Extract audio paths and reference languages (if available)
            audio_paths = []
            reference_langs = []
            
            for item in batch_items:
                audio_path = item.get('audio_filepath', item.get('audio_path', ''))
                reference_lang = item.get('language', item.get('reference_lang', ''))
                audio_paths.append(audio_path)
                reference_langs.append(reference_lang)
            
            # Load audio batch
            audio_data, valid_paths = self.load_audio_batch(audio_paths)
            
            # Perform batch inference
            if audio_data:
                predictions = self.infer_batch(audio_data, valid_paths)
            else:
                predictions = [None] * len(audio_paths)
            
            # Create results
            valid_idx = 0
            for audio_path, reference_lang in zip(audio_paths, reference_langs):
                if audio_path in valid_paths:
                    prediction = predictions[valid_idx] if valid_idx < len(predictions) else None
                    valid_idx += 1
                else:
                    prediction = None
                
                result = {
                    'audio_filepath': audio_path,
                    'reference_language': reference_lang,
                    'predicted_language': prediction['language'] if prediction else None,
                    'confidence': prediction['confidence'] if prediction else None,
                    'lang_id': prediction['lang_id'] if prediction else None,
                    'success': prediction is not None
                }
                results.append(result)
        
        # Log progress less frequently
        with self.lock:
            logger.info(f"Processed batch of {len(test_items)} items")
        
        return results
    
    def run_test(self, manifest_path, output_path):
        """Run LID test on the entire manifest"""
        # Load manifest
        logger.info(f"Loading manifest from {manifest_path}...")
        with open(manifest_path, 'r', encoding='utf-8') as f:
            test_data = []
            for line in f:
                line = line.strip()
                if line:
                    test_data.append(json.loads(line))
        
        logger.info(f"Loaded {len(test_data)} test samples")
        
        # Split data into batches for threading (larger batches for GPU efficiency)
        thread_batch_size = max(self.batch_size * 4, len(test_data) // self.max_workers)
        batches = [test_data[i:i + thread_batch_size] for i in range(0, len(test_data), thread_batch_size)]
        
        logger.info(f"Split into {len(batches)} thread batches, each with ~{thread_batch_size} samples")
        logger.info(f"GPU batch size: {self.batch_size}")
        
        # Process with multiple threads
        all_results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {executor.submit(self.process_batch, batch): batch for batch in batches}
            
            for future in tqdm(as_completed(future_to_batch), total=len(batches), desc="Processing thread batches"):
                batch_results = future.result()
                all_results.extend(batch_results)
        
        # Save results
        logger.info(f"Saving results to {output_path}...")
        with open(output_path, 'w', encoding='utf-8') as f:
            for result in all_results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
        
        return all_results
    
    def calculate_metrics(self, results):
        """Calculate LID accuracy and other metrics"""
        successful_results = [r for r in results if r['success'] and r['predicted_language']]
        
        if not successful_results:
            logger.warning("No successful predictions found!")
            return None
        
        # Calculate accuracy (if reference languages are provided)
        correct_predictions = 0
        total_with_reference = 0
        
        for result in successful_results:
            if result['reference_language']:  # If reference language is provided
                total_with_reference += 1
                if result['predicted_language'] == result['reference_language']:
                    correct_predictions += 1
        
        # Language distribution
        predicted_langs = [r['predicted_language'] for r in successful_results]
        lang_distribution = Counter(predicted_langs)
        
        # Confidence statistics
        confidences = [r['confidence'] for r in successful_results if r['confidence'] is not None]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        metrics = {
            'total_samples': len(results),
            'successful_samples': len(successful_results),
            'success_rate': len(successful_results) / len(results),
            'samples_with_reference': total_with_reference,
            'accuracy': correct_predictions / total_with_reference if total_with_reference > 0 else None,
            'average_confidence': avg_confidence,
            'language_distribution': dict(lang_distribution.most_common()),
            'unique_languages_detected': len(lang_distribution)
        }
        
        return metrics

def main():
    parser = argparse.ArgumentParser(description='MMS LID Testing Script (GPU Optimized)')
    parser.add_argument('--manifest', default='manifest.json', help='Path to manifest file')
    parser.add_argument('--output', default='mms_lid_output.json', help='Output file path')
    parser.add_argument('--model_path', default='facebook/mms-lid-512', help='Path to MMS LID model')
    parser.add_argument('--max_workers', type=int, default=2, help='Number of worker threads (reduce for GPU)')
    parser.add_argument('--batch_size', type=int, default=8, help='GPU batch size for inference')
    parser.add_argument('--device', default=None, help='Device to use (cuda/cpu)')
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = MMSLIDTester(
        model_path=args.model_path,
        max_workers=args.max_workers,
        batch_size=args.batch_size,
        device=args.device
    )
    
    # Run test
    results = tester.run_test(args.manifest, args.output)
    
    # Calculate metrics
    logger.info("Calculating metrics...")
    metrics = tester.calculate_metrics(results)
    
    if metrics:
        print("\n" + "="*50)
        print("LID TEST RESULTS")
        print("="*50)
        print(f"Total samples: {metrics['total_samples']}")
        print(f"Successful samples: {metrics['successful_samples']}")
        print(f"Success rate: {metrics['success_rate']:.2%}")
        print(f"Average confidence: {metrics['average_confidence']:.4f}")
        print(f"Unique languages detected: {metrics['unique_languages_detected']}")
        
        if metrics['accuracy'] is not None:
            print(f"Samples with reference: {metrics['samples_with_reference']}")
            print(f"Accuracy: {metrics['accuracy']:.2%}")
        
        print(f"\nTop 10 detected languages:")
        for lang, count in list(metrics['language_distribution'].items())[:10]:
            percentage = count / metrics['successful_samples'] * 100
            print(f"  {lang}: {count} ({percentage:.1f}%)")
        
        # Save metrics
        metrics_path = args.output.replace('.json', '_metrics.json')
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        print(f"\nMetrics saved to: {metrics_path}")
    
    print(f"\nResults saved to: {args.output}")

if __name__ == "__main__":
    main()

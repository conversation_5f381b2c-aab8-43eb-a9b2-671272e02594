# -*- coding: utf-8 -*-
import json
import re
import argparse


def remove_special_characters(text):
    remove_chars = r'[·’!"\#$%&\'()＃！（）*+,-./:;<=>?\@，：?￥■★、…．＞【】［］《》？“”‘’\[\\]^_`{|}~]+'
    # remove
    sent = text.replace('\ufeff', '').replace('\uff11', '')
    sent = re.sub(remove_chars, '', sent) # 去除不发音字符
    sent = re.sub(r'\(.*?\)', '', sent)  # 去除括号内的内容（通常是注释内容，不发音）
    sent = re.sub(r'\[.*?\]', '', sent)  # 去除括号内的内容（通常是注释内容，不发音）
    # split
    sent = re.sub(r'[\u0F0B-\u0F0E]+', ' ', sent)  # 将音节分隔符 U+0F0B、音节分隔符 U+0F0C 、藏文句号 U+0F0D 、双句号 U+0F0E 均替换成空格
    sent = re.sub(r'([\d\u0F20-\u0F29])([\u0F00-\u0FFF])', r'\1 \2', sent)  # add space before number 藏文数字（U+0F20–U+0F29）
    sent = re.sub(r'([\u0F00-\u0FFF])([\d\u0F20-\u0F29])', r'\1 \2', sent)  # add space afer number
    sent = re.sub(r'\s+', ' ', sent)  # 去除多个空格
    return sent.strip()

def find_illegal_characters(text):
    """
    合法范围：
        藏文字符 \u0F00-\u0FFF
        阿拉伯数字
        空格
    """
    illegal_chars = r'[^\u0F00-\u0FFF\d\s]'
    result = re.findall(illegal_chars, text)
    return result

def _test():
    text = "༄༅།།ཞི་ཅིན་ཕིང་གིས་ཀྲུང་དབྱང་སྐུ་ཚབ་ཚོགས་པའི་སྣེ་ཁྲིད་དེ་ལྷ་སར་ཕེབས་འབྱོར་ཟིན་པ་དང་བོད་རང་སྐྱོང་ལྗོངས་དབུ་བརྙེས་ནས་ལོ་ངོ་60འཁོར་བའི་རྟེན་འབྲེལ་མཛད་སྒོར་ཞུགས་པ།"
    text = remove_special_characters(text)
    illegal_characters = find_illegal_characters(text)
    print(illegal_characters)

def main():
    parser = argparse.ArgumentParser(description='Clean Tibetan text')
    parser.add_argument('--input', required=True, help='Input file')
    parser.add_argument('--output', required=True, help='Output file')
    parser.add_argument('--format', required=True, choices=['text', 'json'],
                       help='Format of input file')
    args = parser.parse_args()

    fout = open(args.output, 'w', encoding='utf-8')

    if args.format == "text":
        with open(args.input, 'r', encoding='utf-8')as fr:
            for line in fr.readlines():
                items = line.strip().split(' ')
                if len(items) >= 2:
                    key = items[0]
                    text = ' '.join(items[1:])
                    text = remove_special_characters(text)
                    illegal_characters = find_illegal_characters(text)
                    if illegal_characters:
                        print(f"Warning: Illegal characters found in {key}: {illegal_characters}")
                        continue
                else:
                    continue
                fout.write(f"{key} {text}\n")

    elif args.format == "json":
        with open(args.input, 'r', encoding='utf-8')as fr:
            for line in fr.readlines():
                dic = json.loads(line.strip())
                key = dic['audio_filepath']
                text = dic['text']
                text = remove_special_characters(text)
                illegal_characters = find_illegal_characters(text)
                if illegal_characters:
                    print(f"Warning: Illegal characters found in {key}: {illegal_characters}")
                    continue
                if len(text) < 1:
                    continue
                dic['text_clean'] = text
                fout.write(json.dumps(dic, ensure_ascii=False)+'\n')
    else:
        print(f"Error: Unknown format {args.format}")
    fout.close()

main()
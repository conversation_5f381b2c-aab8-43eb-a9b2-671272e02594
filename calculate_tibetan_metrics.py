#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import re
import jiwer
from collections import OrderedDict
from transformers.models.whisper.english_normalizer import BasicTextNormalizer

def normalize_tibetan_text(text):
    """
    Normalize Tibetan text for accurate WER/CER evaluation

    Steps:
    1. Replace all tsheg (་) with spaces
    2. Replace all '▁' with spaces
    3. Remove all spaces
    4. Apply Whisper BasicTextNormalizer

    Args:
        text (str): Input Tibetan text

    Returns:
        str: Normalized text
    """
    if not text or not isinstance(text, str):
        return ""

    # Step 1: Replace tsheg (་) with spaces
    text = text.replace('་', ' ')

    # Step 2: Replace '▁' with spaces
    text = text.replace('▁', ' ')

    # Step 3: Remove all spaces
    text = re.sub(r'\s+', '', text)

    # Step 4: Apply Whisper BasicTextNormalizer
    normalizer = BasicTextNormalizer()
    text = normalizer(text)

    return text

def load_text_file(file_path):
    """
    Load text file with format: key<space>text
    Returns dictionary mapping key to text
    """
    text_dict = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line:
                # Split by first space to separate key and text
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    key, text = parts
                    text_dict[key] = text
                else:
                    print(f"Warning: Skipping malformed line {line_num} in {file_path}: {line}")
    
    return text_dict

def match_ref_hyp(ref_dict, hyp_dict):
    """
    Match reference and hypothesis texts by key
    Returns lists of matched references and hypotheses
    """
    references = []
    hypotheses = []
    matched_keys = []
    
    # Find common keys
    common_keys = set(ref_dict.keys()) & set(hyp_dict.keys())
    
    for key in sorted(common_keys):  # Sort for consistent ordering
        references.append(ref_dict[key])
        hypotheses.append(hyp_dict[key])
        matched_keys.append(key)
    
    return references, hypotheses, matched_keys

def calculate_tibetan_metrics(references, hypotheses):
    """
    Calculate CER and WER for Tibetan text with improved normalization

    Args:
        references (list): List of reference texts
        hypotheses (list): List of predicted texts

    Returns:
        dict: Metrics including CER, WER, and normalized texts
    """
    # Normalize all texts using the improved method
    norm_references = [normalize_tibetan_text(ref) for ref in references]
    norm_hypotheses = [normalize_tibetan_text(hyp) for hyp in hypotheses]

    # Calculate metrics
    cer = jiwer.cer(norm_references, norm_hypotheses)
    wer = jiwer.wer(norm_references, norm_hypotheses)

    return {
        'cer': cer,
        'wer': wer,
        'normalized_references': norm_references,
        'normalized_hypotheses': norm_hypotheses
    }

def calculate_detailed_metrics(references, hypotheses, matched_keys):
    """Calculate detailed metrics with per-sample analysis using improved normalization"""

    if not references or not hypotheses:
        print("Error: No matched samples found!")
        return None

    # Calculate normalized metrics
    tibetan_metrics = calculate_tibetan_metrics(references, hypotheses)

    # Calculate raw metrics for comparison
    raw_cer = jiwer.cer(references, hypotheses)
    raw_wer = jiwer.wer(references, hypotheses)

    # Per-sample metrics (optional, for detailed analysis)
    per_sample_metrics = []
    for ref, hyp, key in zip(references, hypotheses, matched_keys):
        norm_ref = normalize_tibetan_text(ref)
        norm_hyp = normalize_tibetan_text(hyp)

        sample_cer = jiwer.cer([norm_ref], [norm_hyp])
        sample_wer = jiwer.wer([norm_ref], [norm_hyp])

        per_sample_metrics.append({
            'key': key,
            'cer': sample_cer,
            'wer': sample_wer,
            'original_ref': ref,
            'original_hyp': hyp,
            'normalized_ref': norm_ref,
            'normalized_hyp': norm_hyp
        })

    metrics = {
        'total_samples': len(references),
        'normalization_method': 'improved_tibetan',
        'cer': tibetan_metrics['cer'],
        'wer': tibetan_metrics['wer'],
        'raw_cer': raw_cer,
        'raw_wer': raw_wer,
        'per_sample_metrics': per_sample_metrics
    }

    # Add some examples for inspection
    if len(references) > 0:
        metrics['examples'] = {
            'first_sample': {
                'key': matched_keys[0],
                'original_reference': references[0],
                'normalized_reference': tibetan_metrics['normalized_references'][0],
                'original_hypothesis': hypotheses[0],
                'normalized_hypothesis': tibetan_metrics['normalized_hypotheses'][0]
            }
        }

    return metrics

def main():
    parser = argparse.ArgumentParser(description='Calculate WER/CER for Tibetan text files (Improved Normalization)')
    parser.add_argument('--ref', required=True, help='Reference text file (key<space>text format)')
    parser.add_argument('--hyp', required=True, help='Hypothesis text file (key<space>text format)')
    parser.add_argument('--output', default='metrics_result.json', help='Output metrics file')
    parser.add_argument('--save_details', action='store_true',
                       help='Save detailed per-sample metrics')

    args = parser.parse_args()

    print("="*60)
    print("TIBETAN WER/CER CALCULATOR (IMPROVED NORMALIZATION)")
    print("="*60)
    print(f"Reference file: {args.ref}")
    print(f"Hypothesis file: {args.hyp}")
    print(f"Normalization method: Improved Tibetan (tsheg->space, ▁->space, remove spaces, BasicTextNormalizer)")
    print(f"Output file: {args.output}")
    print("-" * 60)
    
    # Load files
    print("Loading reference file...")
    ref_dict = load_text_file(args.ref)
    print(f"Loaded {len(ref_dict)} reference entries")
    
    print("Loading hypothesis file...")
    hyp_dict = load_text_file(args.hyp)
    print(f"Loaded {len(hyp_dict)} hypothesis entries")
    
    # Match by keys
    print("Matching references and hypotheses...")
    references, hypotheses, matched_keys = match_ref_hyp(ref_dict, hyp_dict)
    
    print(f"Matched samples: {len(matched_keys)}")
    print(f"Unmatched in reference: {len(ref_dict) - len(matched_keys)}")
    print(f"Unmatched in hypothesis: {len(hyp_dict) - len(matched_keys)}")
    
    if len(matched_keys) == 0:
        print("Error: No matching keys found between reference and hypothesis files!")
        return
    
    # Calculate metrics
    print(f"\nCalculating metrics with improved Tibetan normalization...")
    metrics = calculate_detailed_metrics(references, hypotheses, matched_keys)
    
    if metrics:
        print("\n" + "="*50)
        print("RESULTS")
        print("="*50)
        print(f"Total matched samples: {metrics['total_samples']}")
        print(f"Normalization method: {metrics['normalization_method']}")
        print(f"Character Error Rate (CER): {metrics['cer']:.4f}")
        print(f"Word Error Rate (WER): {metrics['wer']:.4f}")
        print(f"Raw CER (no normalization): {metrics['raw_cer']:.4f}")
        print(f"Raw WER (no normalization): {metrics['raw_wer']:.4f}")
        
        # Show normalization example
        if 'examples' in metrics:
            print("\n" + "-"*50)
            print("NORMALIZATION EXAMPLE:")
            print("-"*50)
            print(f"Key: {metrics['examples']['first_sample']['key']}")
            print(f"Original Reference: {metrics['examples']['first_sample']['original_reference']}")
            print(f"Normalized Reference: {metrics['examples']['first_sample']['normalized_reference']}")
            print(f"Original Hypothesis: {metrics['examples']['first_sample']['original_hypothesis']}")
            print(f"Normalized Hypothesis: {metrics['examples']['first_sample']['normalized_hypothesis']}")
        
        # Prepare output data
        output_data = {
            'summary': {
                'total_samples': metrics['total_samples'],
                'normalization_method': metrics['normalization_method'],
                'cer': metrics['cer'],
                'wer': metrics['wer'],
                'raw_cer': metrics['raw_cer'],
                'raw_wer': metrics['raw_wer']
            }
        }
        
        if args.save_details:
            output_data['per_sample_metrics'] = metrics['per_sample_metrics']
        
        if 'examples' in metrics:
            output_data['examples'] = metrics['examples']
        
        # Save metrics
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"\nMetrics saved to: {args.output}")
        
        if args.save_details:
            print("Detailed per-sample metrics included in output file")

if __name__ == "__main__":
    main()

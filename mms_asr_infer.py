import os
import sys
import soundfile as sf
import numpy as np
import torch
from transformers import Wav2Vec2ForCTC, AutoProcessor
from transformers import Wav2Vec2ForSequenceClassification, AutoFeatureExtractor

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

def load_audio_file(file_path, target_sr=16000):
    data, samplerate = sf.read(file_path)
    if samplerate != target_sr:
        print(f"Warning: Audio file {file_path} has sample rate {samplerate}, resampling to {target_sr}")
        import librosa
        data = librosa.resample(data, orig_sr=samplerate, target_sr=target_sr)
    return np.asarray(data, dtype=np.float32)


def load_asr_model(model_id="./mms_1b_all", target_lang="bod"):  # "facebook/mms-1b-all"
    # load model
    processor = AutoProcessor.from_pretrained(model_id, target_lang=target_lang)
    model = Wav2Vec2ForCTC.from_pretrained(model_id, target_lang=target_lang, ignore_mismatched_sizes=True)
    
    if save_model_to_disk:
        disk_dir = model_id.split('/')[-1]
        model.save_pretrained(disk_dir)
        processor.save_pretrained(disk_dir)
        try:
            model.save_adapter(disk_dir, target_lang)
        except:
            print(f"Model save adapter faild!")
            pass
    
    return processor, model

def asr_infer(audio_path, target_lang="bod"):
    # load sample
    try:
        sample = load_audio_file(audio_path, target_sr=16000)
        print("Audio sample loaded successfully. Shape:", sample.shape)
    except Exception as e:
        print(f"An error occurred while loading audio: {e}")

    # inference
    inputs = processor(sample, sampling_rate=16000, return_tensors="pt")
    with torch.no_grad():
        outputs = model(**inputs).logits
    ids = torch.argmax(outputs, dim=-1)[0]
    transcription = processor.decode(ids)
    print(f"\"{audio_path}\"")
    print(f"ASR result: \"{transcription}\"")
    print()
    print(transcription)


def load_lid_model(model_id="facebook/mms-lid-512"):
    # load model
    processor = AutoFeatureExtractor.from_pretrained(model_id)
    model = Wav2Vec2ForSequenceClassification.from_pretrained(model_id)

    if save_model_to_disk:
        disk_dir = model_id.split('/')[-1]
        model.save_pretrained(disk_dir)
        processor.save_pretrained(disk_dir)

    return processor, model


def lid_infer(audio_path):
    # load sample
    try:
        sample = load_audio_file(audio_path, target_sr=16000)
        print("Audio sample loaded successfully. Shape:", sample.shape)
    except Exception as e:
        print(f"An error occurred while loading audio: {e}")

    # inference
    inputs = processor(sample, sampling_rate=16_000, return_tensors="pt")
    with torch.no_grad():
        outputs = model(**inputs).logits
    lang_id = torch.argmax(outputs, dim=-1)[0].item()
    detected_lang = model.config.id2label[lang_id]
    print(f"\"{audio_path}\"")
    print(f"LID result: \"{detected_lang}\"")
    print()


save_model_to_disk = True
func = lid_infer
processor, model = load_lid_model()
audio_path = "/mnt/nvme/liuhuan/datasets/bo_test/xbmu-amdo31/data/wav/test/a_0_cacm/a_0_cacm-A70_31050.wav"

# process input
if len(sys.argv) > 1:
    audio_path = sys.argv[1]
is_file   = os.path.isfile(audio_path)
is_folder = os.path.isdir(audio_path)

if is_file:
    func(audio_path)
elif is_folder:
    from glob import glob
    for audio in glob(f"{audio_path}/*wav"):
        func(audio)
else:
    print(f"Invalid path: {audio_path}")
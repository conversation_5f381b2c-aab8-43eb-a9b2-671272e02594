import os
import soundfile as sf
import numpy as np
import torch
from transformers import Wav2Vec2ForCTC, AutoProcessor
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

# `datasets` lib not support
def load_audio_file(file_path, target_sr=16000):
    data, samplerate = sf.read(file_path)
    if samplerate != target_sr:
        print(f"Warning: Audio file {file_path} has sample rate {samplerate}, resampling to {target_sr}")
        import librosa
        data = librosa.resample(data, orig_sr=samplerate, target_sr=target_sr)
    return np.asarray(data, dtype=np.float32)


# load English sample
en_audio_path = "/mnt/nvme/liuhuan/en_test.wav"
try:
    en_sample = load_audio_file(en_audio_path, target_sr=16000)
    print("English sample loaded successfully. Shape:", en_sample.shape)
except Exception as e:
    print(f"An error occurred while loading audio: {e}")

# load model
# model_id = "facebook/mms-1b-all"  # .from_pretrained -> .cache
model_id = "./mms_1b_all"           # .save_pretrained -> ./mms_1b_all
target_lang = "eng"

processor = AutoProcessor.from_pretrained(model_id, target_lang=target_lang)
model = Wav2Vec2ForCTC.from_pretrained(model_id, target_lang=target_lang, ignore_mismatched_sizes=True)


# inference
inputs = processor(en_sample, sampling_rate=16000, return_tensors="pt")
with torch.no_grad():
    outputs = model(**inputs).logits
ids = torch.argmax(outputs, dim=-1)[0]
transcription = processor.decode(ids)
print(en_audio_path)
print(transcription)
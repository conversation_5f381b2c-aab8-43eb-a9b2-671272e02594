2025.08.28 更新
主要优化：
1. 旧版本在强噪音环境下vad失效导致无法有效断句
2. 旧版本在强噪音环境下识别结果质量低下，新版本增加非实时模型对分句做二次解码，替换实时识别结果，返回消息中的`segement_index` 指示分句序号，同序号的最后一个结果为二次解码的分句优化结果。
3. 客户端的收发消息接口均不变。


相关信息：
路径：/data/asr/x86_ministream_v0828
容器：ministream_v0828
默认服务端口: 10050  （可以通过 conf/config.yaml 中 `port` 参数修改）


1.  启动单语种asr服务：
```bash
# 进入容器 /ws
python app.py en # 默认服务端口10050启动
python app.py en --port 10051 # 指定服务端口启动
```

2. 快速测试
```bash
python fast_test.py  wavs/en/en_test.wav  # 向默认服务端口10050请求
python fast_test.py  wavs/en/en_test.wav  10051 # 向指定服务端口请求
```
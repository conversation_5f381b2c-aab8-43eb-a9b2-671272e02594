import os
import json
import soundfile as sf
import librosa
import numpy as np
import torch
from transformers import Wav2Vec2ForCTC, AutoProcessor
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import jiwer
from tqdm import tqdm
import argparse

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

class MMSASRTester:
    def __init__(self, model_path="./mms_1b_all", target_lang="eng", max_workers=4):
        self.model_path = model_path
        self.target_lang = target_lang
        self.max_workers = max_workers
        self.lock = Lock()
        
        # Load model and processor
        print(f"Loading MMS model from {model_path}...")
        self.processor = AutoProcessor.from_pretrained(model_path, target_lang=target_lang)
        
        self.model = Wav2Vec2ForCTC.from_pretrained(
            model_path, 
            target_lang=target_lang, 
            ignore_mismatched_sizes=True
        )
        print("Model loaded successfully!")

        # switch to target language
        if target_lang != "eng":
            self.processor.tokenizer.set_target_lang(target_lang)
            self.model.load_adapter(target_lang)
            local_target_adapter = f"{model_path}/adapter.{target_lang}.safetensors"
            if not os.path.exists(local_target_adapter):
                self.model.save_adapter(model_path, target_lang)

        # save_pretrained weight
        if not os.path.exists(model_path):
            self.model.save_pretrained(model_path)
            self.processor.save_pretrained(model_path)
        

    def load_audio_file(self, file_path, target_sr=16000):
        """Load and resample audio file"""
        try:
            data, samplerate = sf.read(file_path)
            if samplerate != target_sr:
                print(f"Warning: Audio file {file_path} has sample rate {samplerate}, resampling to {target_sr}")
                data = librosa.resample(data, orig_sr=samplerate, target_sr=target_sr)
            return np.asarray(data, dtype=np.float32)
        except Exception as e:
            print(f"Error loading audio file {file_path}: {e}")
            return None
    
    def infer_single_audio(self, audio_path):
        """Perform inference on a single audio file"""
        try:
            # Load audio
            audio_data = self.load_audio_file(audio_path)
            if audio_data is None:
                return None
            
            # Process and infer
            inputs = self.processor(audio_data, sampling_rate=16000, return_tensors="pt")
            with torch.no_grad():
                outputs = self.model(**inputs).logits
            ids = torch.argmax(outputs, dim=-1)[0]
            transcription = self.processor.decode(ids)
            
            return transcription
        except Exception as e:
            print(f"Error during inference for {audio_path}: {e}")
            return None
    
    def process_batch(self, test_items):
        """Process a batch of test items"""
        results = []
        
        for item in test_items:
            audio_path = item.get('audio_filepath', item.get('audio_path', ''))
            reference_text = item.get('text', item.get('reference', ''))
            
            # Perform inference
            predicted_text = self.infer_single_audio(audio_path)
            
            result = {
                'audio_filepath': audio_path,
                'reference_text': reference_text,
                'predicted_text': predicted_text,
                'success': predicted_text is not None
            }
            results.append(result)
            
            with self.lock:
                print(f"Processed: {audio_path}")
                if predicted_text:
                    print(f"Reference: {reference_text}")
                    print(f"Predicted: {predicted_text}")
                    print("-" * 50)
        
        return results
    
    def run_test(self, manifest_path, output_path):
        """Run test on the entire manifest"""
        # Load manifest
        print(f"Loading manifest from {manifest_path}...")
        with open(manifest_path, 'r', encoding='utf-8') as f:
            test_data = []
            for line in f:
                line = line.strip()
                if line:
                    test_data.append(json.loads(line))
        
        print(f"Loaded {len(test_data)} test samples")
        
        # Split data into batches for threading
        batch_size = max(1, len(test_data) // self.max_workers)
        batches = [test_data[i:i + batch_size] for i in range(0, len(test_data), batch_size)]
        
        # Process with multiple threads
        all_results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {executor.submit(self.process_batch, batch): batch for batch in batches}
            
            for future in tqdm(as_completed(future_to_batch), total=len(batches), desc="Processing batches"):
                batch_results = future.result()
                all_results.extend(batch_results)
        
        # Save results
        print(f"Saving results to {output_path}...")
        with open(output_path, 'w', encoding='utf-8') as f:
            for result in all_results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
        
        return all_results
    
    def calculate_metrics(self, results):
        """Calculate CER and WER metrics"""
        references = []
        hypotheses = []
        
        successful_results = [r for r in results if r['success'] and r['predicted_text']]
        
        for result in successful_results:
            references.append(result['reference_text'])
            hypotheses.append(result['predicted_text'])
        
        if not references:
            print("No successful predictions found!")
            return None
        
        # Calculate metrics
        cer = jiwer.cer(references, hypotheses)
        wer = jiwer.wer(references, hypotheses)
        
        metrics = {
            'total_samples': len(results),
            'successful_samples': len(successful_results),
            'success_rate': len(successful_results) / len(results),
            'cer': cer,
            'wer': wer
        }
        
        return metrics

def main():
    parser = argparse.ArgumentParser(description='MMS ASR Testing Script')
    parser.add_argument('--manifest', default='manifest.json', help='Path to manifest file')
    parser.add_argument('--output', default='mms_asr_output.json', help='Output file path')
    parser.add_argument('--model_path', default='./mms_1b_all', help='Path to MMS model')
    parser.add_argument('--target_lang', default='eng', help='Target language')
    parser.add_argument('--max_workers', type=int, default=4, help='Number of worker threads')
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = MMSASRTester(
        model_path=args.model_path,
        target_lang=args.target_lang,
        max_workers=args.max_workers
    )
    
    # Run test
    results = tester.run_test(args.manifest, args.output)
    
    # Calculate metrics
    print("\nCalculating metrics...")
    metrics = tester.calculate_metrics(results)
    
    if metrics:
        print("\n" + "="*50)
        print("TEST RESULTS")
        print("="*50)
        print(f"Total samples: {metrics['total_samples']}")
        print(f"Successful samples: {metrics['successful_samples']}")
        print(f"Success rate: {metrics['success_rate']:.2%}")
        print(f"Character Error Rate (CER): {metrics['cer']:.4f}")
        print(f"Word Error Rate (WER): {metrics['wer']:.4f}")
        
        # Save metrics
        metrics_path = args.output.replace('.json', '_metrics.json')
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        print(f"\nMetrics saved to: {metrics_path}")
    
    print(f"\nResults saved to: {args.output}")

if __name__ == "__main__":
    main()

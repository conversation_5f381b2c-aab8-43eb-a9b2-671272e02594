import os
import json
import soundfile as sf
import librosa
import numpy as np
import torch
from transformers import Wav2Vec2ForCTC, AutoProcessor
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import jiwer
from tqdm import tqdm
import argparse
import logging
import re


os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

# Setup logging
file_handler = logging.FileHandler("test.log", mode="a", encoding="utf-8")
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[file_handler])
logger = logging.getLogger(__name__)

def normalize_tibetan_text(text, target_format="space"):
    """
    Normalize Tibetan text for consistent evaluation

    Args:
        text (str): Input Tibetan text
        target_format (str): "space" for space-separated, "tsheg" for ་-separated

    Returns:
        str: Normalized text
    """
    if not text or not isinstance(text, str):
        return ""

    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())

    if target_format == "space":
        # Convert ་ to space, but be careful with existing spaces
        # First, handle cases where there might be both ་ and spaces
        text = re.sub(r'་\s*', ' ', text)  # ་ followed by optional space -> space
        text = re.sub(r'\s+', ' ', text)   # Multiple spaces -> single space

    elif target_format == "tsheg":
        # Convert spaces to ་
        text = re.sub(r'\s+', '་', text)
        # Remove any trailing ་
        text = text.rstrip('་')

    return text.strip()

def calculate_tibetan_metrics(references, hypotheses, normalize_format="space"):
    """
    Calculate CER and WER for Tibetan text with proper normalization

    Args:
        references (list): List of reference texts
        hypotheses (list): List of predicted texts
        normalize_format (str): "space" or "tsheg" for normalization format

    Returns:
        dict: Metrics including CER, WER, and normalized texts
    """
    # Normalize all texts to the same format
    norm_references = [normalize_tibetan_text(ref, normalize_format) for ref in references]
    norm_hypotheses = [normalize_tibetan_text(hyp, normalize_format) for hyp in hypotheses]

    # Calculate metrics
    cer = jiwer.cer(norm_references, norm_hypotheses)
    wer = jiwer.wer(norm_references, norm_hypotheses)

    return {
        'cer': cer,
        'wer': wer,
        'normalized_references': norm_references,
        'normalized_hypotheses': norm_hypotheses
    }

class MMSASRTester:
    def __init__(self, model_path="facebook/mms-1b-all", target_lang="eng", max_workers=4, batch_size=8, device=None):
        self.model_path = model_path
        self.target_lang = target_lang
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.lock = Lock()

        # Setup device
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        logger.info(f"Using device: {self.device}")

        # Load model and processor
        logger.info(f"Loading MMS model from {model_path}...")
        self.processor = AutoProcessor.from_pretrained(model_path, target_lang=target_lang)

        self.model = Wav2Vec2ForCTC.from_pretrained(
            model_path,
            target_lang=target_lang,
            ignore_mismatched_sizes=True
        )

        # Move model to device
        self.model = self.model.to(self.device)
        self.model.eval()  # Set to evaluation mode

        logger.info("Model loaded successfully!")

        # switch to target language
        if target_lang != "eng":
            self.processor.tokenizer.set_target_lang(target_lang)
            self.model.load_adapter(target_lang)
            local_target_adapter = f"{model_path}/adapter.{target_lang}.safetensors"
            if not os.path.exists(local_target_adapter):
                self.model.save_adapter(model_path, target_lang)

        # save_pretrained weight
        if not os.path.exists(model_path):
            self.model.save_pretrained(model_path)
            self.processor.save_pretrained(model_path)
        

    def load_audio_file(self, file_path, target_sr=16000):
        """Load and resample audio file"""
        try:
            data, samplerate = sf.read(file_path)
            if samplerate != target_sr:
                data = librosa.resample(data, orig_sr=samplerate, target_sr=target_sr)
            return np.asarray(data, dtype=np.float32)
        except Exception as e:
            logger.error(f"Error loading audio file {file_path}: {e}")
            return None

    def load_audio_batch(self, audio_paths):
        """Load a batch of audio files"""
        audio_data = []
        valid_paths = []

        for path in audio_paths:
            data = self.load_audio_file(path)
            if data is not None:
                audio_data.append(data)
                valid_paths.append(path)

        return audio_data, valid_paths

    def infer_batch(self, audio_batch, audio_paths):
        """Perform batch inference on multiple audio files"""
        try:
            if not audio_batch:
                return [None] * len(audio_paths)

            # Process batch with padding
            inputs = self.processor(
                audio_batch,
                sampling_rate=16000,
                return_tensors="pt",
                padding=True
            )

            # Move inputs to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Inference
            with torch.no_grad():
                outputs = self.model(**inputs).logits

            # Decode results
            transcriptions = []
            for i in range(outputs.shape[0]):
                ids = torch.argmax(outputs[i], dim=-1)
                transcription = self.processor.decode(ids)
                transcriptions.append(transcription)

            return transcriptions

        except Exception as e:
            logger.error(f"Error during batch inference: {e}")
            return [None] * len(audio_paths)
    
    def process_batch(self, test_items):
        """Process a batch of test items with GPU batch inference"""
        results = []

        # Group items into inference batches
        for i in range(0, len(test_items), self.batch_size):
            batch_items = test_items[i:i + self.batch_size]

            # Extract audio paths and reference texts
            audio_paths = []
            reference_texts = []

            for item in batch_items:
                audio_path = item.get('audio_filepath', item.get('audio_path', ''))
                reference_text = item.get('text', item.get('reference', ''))
                audio_paths.append(audio_path)
                reference_texts.append(reference_text)

            # Load audio batch
            audio_data, valid_paths = self.load_audio_batch(audio_paths)

            # Perform batch inference
            if audio_data:
                predictions = self.infer_batch(audio_data, valid_paths)
            else:
                predictions = [None] * len(audio_paths)

            # Create results
            valid_idx = 0
            for audio_path, reference_text in zip(audio_paths, reference_texts):
                if audio_path in valid_paths:
                    predicted_text = predictions[valid_idx] if valid_idx < len(predictions) else None
                    valid_idx += 1
                else:
                    predicted_text = None

                result = {
                    'audio_filepath': audio_path,
                    'reference_text': reference_text,
                    'predicted_text': predicted_text,
                    'success': predicted_text is not None
                }
                results.append(result)

        # Log progress less frequently
        with self.lock:
            logger.info(f"Processed batch of {len(test_items)} items")

        return results
    
    def run_test(self, manifest_path, output_path):
        """Run test on the entire manifest"""
        # Load manifest
        logger.info(f"Loading manifest from {manifest_path}...")
        with open(manifest_path, 'r', encoding='utf-8') as f:
            test_data = []
            for line in f:
                line = line.strip()
                if line:
                    test_data.append(json.loads(line))

        logger.info(f"Loaded {len(test_data)} test samples")

        # Split data into batches for threading (larger batches for GPU efficiency)
        thread_batch_size = max(self.batch_size * 4, len(test_data) // self.max_workers)
        batches = [test_data[i:i + thread_batch_size] for i in range(0, len(test_data), thread_batch_size)]

        logger.info(f"Split into {len(batches)} thread batches, each with ~{thread_batch_size} samples")
        logger.info(f"GPU batch size: {self.batch_size}")

        # Process with multiple threads
        all_results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {executor.submit(self.process_batch, batch): batch for batch in batches}

            for future in tqdm(as_completed(future_to_batch), total=len(batches), desc="Processing thread batches"):
                batch_results = future.result()
                all_results.extend(batch_results)

        # Save results
        logger.info(f"Saving results to {output_path}...")
        with open(output_path, 'w', encoding='utf-8') as f:
            for result in all_results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')

        return all_results
    
    def calculate_metrics(self, results, normalize_format="space"):
        """Calculate CER and WER metrics with Tibetan text normalization"""
        references = []
        hypotheses = []

        successful_results = [r for r in results if r['success'] and r['predicted_text']]

        for result in successful_results:
            references.append(result['reference_text'])
            hypotheses.append(result['predicted_text'])

        if not references:
            logger.warning("No successful predictions found!")
            return None

        # Calculate metrics with normalization
        if self.target_lang in ['adx', 'bod', 'khg']:
            tibetan_metrics = calculate_tibetan_metrics(references, hypotheses, normalize_format)
            
            # Also calculate raw metrics for comparison
            raw_cer = jiwer.cer(references, hypotheses)
            raw_wer = jiwer.wer(references, hypotheses)

            metrics = {
                'total_samples': len(results),
                'successful_samples': len(successful_results),
                'success_rate': len(successful_results) / len(results),
                'cer': tibetan_metrics['cer'],
                'wer': tibetan_metrics['wer'],
                'raw_cer': raw_cer,
                'raw_wer': raw_wer,
                'normalization_format': normalize_format
            }

            # Save some examples of normalized texts for inspection
            if len(tibetan_metrics['normalized_references']) > 0:
                metrics['examples'] = {
                    'original_reference': references[0],
                    'normalized_reference': tibetan_metrics['normalized_references'][0],
                    'original_hypothesis': hypotheses[0],
                    'normalized_hypothesis': tibetan_metrics['normalized_hypotheses'][0]
                }

            return metrics
        else:
            # Calculate metrics
            cer = jiwer.cer(references, hypotheses)
            wer = jiwer.wer(references, hypotheses)
            metrics = {
                'total_samples': len(results),
                'successful_samples': len(successful_results),
                'success_rate': len(successful_results) / len(results),
                'cer': cer,
                'wer': wer
            }
            return metrics

        

def main():
    parser = argparse.ArgumentParser(description='MMS ASR Testing Script (GPU Optimized)')
    parser.add_argument('--manifest', default='manifest.json', help='Path to manifest file')
    parser.add_argument('--output', default='mms_asr_output.json', help='Output file path')
    parser.add_argument('--model_path', default='facebook/mms-1b-all', help='Path to MMS model')
    parser.add_argument('--target_lang', default='eng', help='Target language')
    parser.add_argument('--max_workers', type=int, default=2, help='Number of worker threads (reduce for GPU)')
    parser.add_argument('--batch_size', type=int, default=8, help='GPU batch size for inference')
    parser.add_argument('--device', default=None, help='Device to use (cuda/cpu)')
    parser.add_argument('--normalize_format', default='space', choices=['space', 'tsheg'],
                       help='Text normalization format for Tibetan (space or tsheg)')

    args = parser.parse_args()

    # Initialize tester
    tester = MMSASRTester(
        model_path=args.model_path,
        target_lang=args.target_lang,
        max_workers=args.max_workers,
        batch_size=args.batch_size,
        device=args.device
    )

    # Run test
    results = tester.run_test(args.manifest, args.output)

    # Calculate metrics
    logger.info("Calculating metrics...")
    metrics = tester.calculate_metrics(results, args.normalize_format)

    if metrics:
        print("\n" + "="*50)
        print("TEST RESULTS")
        print("="*50)
        print(f"Total samples: {metrics['total_samples']}")
        print(f"Successful samples: {metrics['successful_samples']}")
        print(f"Success rate: {metrics['success_rate']:.2%}")
        print(f"Normalization format: {metrics['normalization_format']}")
        print(f"Character Error Rate (CER): {metrics['cer']:.4f}")
        print(f"Word Error Rate (WER): {metrics['wer']:.4f}")
        print(f"Raw CER (no normalization): {metrics['raw_cer']:.4f}")
        print(f"Raw WER (no normalization): {metrics['raw_wer']:.4f}")

        # Show normalization example
        if 'examples' in metrics:
            print("\n" + "-"*50)
            print("NORMALIZATION EXAMPLE:")
            print("-"*50)
            print(f"Original Reference: {metrics['examples']['original_reference']}")
            print(f"Normalized Reference: {metrics['examples']['normalized_reference']}")
            print(f"Original Hypothesis: {metrics['examples']['original_hypothesis']}")
            print(f"Normalized Hypothesis: {metrics['examples']['normalized_hypothesis']}")

        # Save metrics
        metrics_path = args.output.replace('.json', '_metrics.json')
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        print(f"\nMetrics saved to: {metrics_path}")

    print(f"\nResults saved to: {args.output}")

if __name__ == "__main__":
    main()

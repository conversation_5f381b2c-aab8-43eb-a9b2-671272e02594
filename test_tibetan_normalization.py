#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(__file__))

from mms_asr_test import normalize_tibetan_text, calculate_tibetan_metrics

def test_normalization():
    """Test Tibetan text normalization"""
    
    # Test cases
    test_cases = [
        {
            'name': 'Your example',
            'reference': 'འུ བཟོ འདི ན ཟ ཁང ར ཡོད ཅིག འཐུང དགོ ན སོང ང ཐུངས ར མི རིགས གྲངས ཉུང ང འཕྲོད ནོ ཟ མ དག དག ཡིན',
            'predicted': 'ཨི་གཟོ་འདི་ན་གས་ཁང་ར་ཡགཅིག་ཐོང་དགོས་ན་སུང་བ་ཐོང་ར་མིག་རིགས་དྲང་མྱོང་བ་ཁྲོད་ནོ་བས་མ་དག་ཏག་ཡིན'
        },
        {
            'name': 'Mixed format',
            'reference': 'བཀྲ་ཤིས བདེ་ལེགས',
            'predicted': 'བཀྲ ཤིས་བདེ་ལེགས'
        }
    ]
    
    print("="*60)
    print("TIBETAN TEXT NORMALIZATION TEST")
    print("="*60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {case['name']} ---")
        
        ref = case['reference']
        pred = case['predicted']
        
        print(f"Original Reference:  {ref}")
        print(f"Original Predicted:  {pred}")
        
        # Test space normalization
        ref_space = normalize_tibetan_text(ref, "space")
        pred_space = normalize_tibetan_text(pred, "space")
        
        print(f"\nSpace-normalized Reference:  {ref_space}")
        print(f"Space-normalized Predicted:  {pred_space}")
        
        # Test tsheg normalization  
        ref_tsheg = normalize_tibetan_text(ref, "tsheg")
        pred_tsheg = normalize_tibetan_text(pred, "tsheg")
        
        print(f"\nTsheg-normalized Reference:  {ref_tsheg}")
        print(f"Tsheg-normalized Predicted:  {pred_tsheg}")
        
        # Calculate metrics
        space_metrics = calculate_tibetan_metrics([ref], [pred], "space")
        tsheg_metrics = calculate_tibetan_metrics([ref], [pred], "tsheg")
        
        print(f"\nMetrics (space normalization):")
        print(f"  CER: {space_metrics['cer']:.4f}")
        print(f"  WER: {space_metrics['wer']:.4f}")
        
        print(f"\nMetrics (tsheg normalization):")
        print(f"  CER: {tsheg_metrics['cer']:.4f}")
        print(f"  WER: {tsheg_metrics['wer']:.4f}")
        
        print("-" * 60)

def test_edge_cases():
    """Test edge cases"""
    print("\n" + "="*60)
    print("EDGE CASES TEST")
    print("="*60)
    
    edge_cases = [
        ("", "empty string"),
        ("   ", "whitespace only"),
        ("བཀྲ་ཤིས་   བདེ་ལེགས   ", "extra whitespace"),
        ("བཀྲ ཤིས བདེ ལེགས", "space separated"),
        ("བཀྲ་ཤིས་བདེ་ལེགས", "tsheg separated"),
        ("བཀྲ ཤིས་བདེ ལེགས", "mixed format")
    ]
    
    for text, description in edge_cases:
        print(f"\nTest: {description}")
        print(f"Input: '{text}'")
        print(f"Space format: '{normalize_tibetan_text(text, 'space')}'")
        print(f"Tsheg format: '{normalize_tibetan_text(text, 'tsheg')}'")

if __name__ == "__main__":
    test_normalization()
    test_edge_cases()
    
    print("\n" + "="*60)
    print("RECOMMENDATION")
    print("="*60)
    print("Based on your example, I recommend using 'space' normalization")
    print("because your reference text uses spaces as separators.")
    print("\nUsage:")
    print("python mms_asr_test.py --target_lang bod --normalize_format space")

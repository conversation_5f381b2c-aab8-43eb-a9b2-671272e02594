import os
import json
import argparse
from pathlib import Path

def load_transcript(transcript_path):
    """Load transcript file and return a dictionary mapping audio_id to text"""
    transcript_dict = {}
    
    with open(transcript_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                # Split by first space to separate audio_id and text
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    audio_id, text = parts
                    transcript_dict[audio_id] = text
                else:
                    print(f"Warning: Skipping malformed line: {line}")
    
    return transcript_dict

def find_audio_files(wav_dir, split='test'):
    """Find all audio files in the specified split directory"""
    audio_files = []
    split_dir = os.path.join(wav_dir, split)
    
    if not os.path.exists(split_dir):
        print(f"Warning: Split directory {split_dir} does not exist")
        return audio_files
    
    # Walk through all subdirectories
    for root, dirs, files in os.walk(split_dir):
        for file in files:
            if file.endswith('.wav'):
                audio_path = os.path.join(root, file)
                # Extract audio_id from filename (remove .wav extension)
                audio_id = os.path.splitext(file)[0]
                audio_files.append((audio_id, audio_path))
    
    return audio_files

def generate_manifest(data_dir, output_path, split='test'):
    """Generate manifest.json file from the dataset"""
    
    # Paths
    transcript_path = os.path.join(data_dir, 'transcript', 'transcript.txt')
    wav_dir = os.path.join(data_dir, 'wav')
    
    # Check if paths exist
    if not os.path.exists(transcript_path):
        print(f"Error: Transcript file not found: {transcript_path}")
        return
    
    if not os.path.exists(wav_dir):
        print(f"Error: WAV directory not found: {wav_dir}")
        return
    
    print(f"Loading transcript from: {transcript_path}")
    transcript_dict = load_transcript(transcript_path)
    print(f"Loaded {len(transcript_dict)} transcript entries")
    
    print(f"Finding audio files in: {os.path.join(wav_dir, split)}")
    audio_files = find_audio_files(wav_dir, split)
    print(f"Found {len(audio_files)} audio files")
    
    # Generate manifest entries
    manifest_entries = []
    matched_count = 0
    unmatched_audio = []
    
    for audio_id, audio_path in audio_files:
        if audio_id in transcript_dict:
            entry = {
                "audio_filepath": audio_path,
                "text": transcript_dict[audio_id]
            }
            manifest_entries.append(entry)
            matched_count += 1
        else:
            unmatched_audio.append(audio_id)
    
    print(f"\nMatching results:")
    print(f"Matched: {matched_count}")
    print(f"Unmatched audio files: {len(unmatched_audio)}")
    
    if unmatched_audio:
        print(f"First 10 unmatched audio IDs:")
        for i, audio_id in enumerate(unmatched_audio[:10]):
            print(f"  {audio_id}")
        if len(unmatched_audio) > 10:
            print(f"  ... and {len(unmatched_audio) - 10} more")
    
    # Save manifest
    print(f"\nSaving manifest to: {output_path}")
    with open(output_path, 'w', encoding='utf-8') as f:
        for entry in manifest_entries:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"Manifest generated successfully with {len(manifest_entries)} entries")
    
    # Show some examples
    if manifest_entries:
        print(f"\nFirst 3 manifest entries:")
        for i, entry in enumerate(manifest_entries[:3]):
            print(f"Entry {i+1}:")
            print(f"  Audio: {entry['audio_filepath']}")
            print(f"  Text: {entry['text']}")
            print()

def main():
    parser = argparse.ArgumentParser(description='Generate manifest.json from Tibetan dataset')
    parser.add_argument('--data_dir', 
                       default='/mnt/nvme/liuhuan/datasets/bo_test/xbmu-amdo31/data',
                       help='Path to dataset directory')
    parser.add_argument('--output', 
                       default='manifest.json',
                       help='Output manifest file path')
    parser.add_argument('--split', 
                       default='test',
                       choices=['train', 'dev', 'test'],
                       help='Dataset split to process')
    
    args = parser.parse_args()
    
    # Convert to absolute paths
    data_dir = os.path.abspath(args.data_dir)
    output_path = os.path.abspath(args.output)
    
    print(f"Dataset directory: {data_dir}")
    print(f"Output file: {output_path}")
    print(f"Split: {args.split}")
    print("-" * 50)
    
    generate_manifest(data_dir, output_path, args.split)

if __name__ == "__main__":
    main()

import argparse
import json
from norm import remove_special_characters, find_illegal_characters

def extract_all_chars(batch):
    all_text = " ".join(batch)
    vocab = list(set(all_text))
    return vocab

def main():
    parser = argparse.ArgumentParser(description="Generate vocab.txt from manifest.json")
    parser.add_argument("--manifest", type=str, required=True, help="Path to manifest.json")
    parser.add_argument("--vocab", type=str, required=True, help="Path to vocab.txt")
    args = parser.parse_args()

    with open(args.manifest, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        lines = [json.loads(line)['text'] for line in lines]
        lines = [remove_special_characters(line) for line in lines if not find_illegal_characters(line)]
        vocab_list = extract_all_chars(lines)
        vocab_dict = {v: k for k, v in enumerate(sorted(vocab_list))}
        vocab_dict["|"] = vocab_dict[" "]
        del vocab_dict[" "]
        vocab_dict["[UNK]"] = len(vocab_dict)
        vocab_dict["[PAD]"] = len(vocab_dict)
        print("length of vocab:", len(vocab_dict))

        target_lang = "bod"
        new_vocab_dict = {target_lang: vocab_dict}

        with open(args.vocab, 'w') as vocab_file:
            json.dump(new_vocab_dict, vocab_file, ensure_ascii=False, indent=4)
        
if __name__ == "__main__":
    main()

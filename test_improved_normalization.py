#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(__file__))

from calculate_tibetan_metrics import normalize_tibetan_text, calculate_tibetan_metrics

def test_improved_normalization():
    """Test the improved Tibetan text normalization"""
    
    print("="*70)
    print("IMPROVED TIBETAN TEXT NORMALIZATION TEST")
    print("="*70)
    
    # Test cases with various formats
    test_cases = [
        {
            'name': 'Your original example',
            'reference': 'འུ བཟོ འདི ན ཟ ཁང ར ཡོད ཅིག འཐུང དགོ ན སོང ང ཐུངས ར མི རིགས གྲངས ཉུང ང འཕྲོད ནོ ཟ མ དག དག ཡིན',
            'predicted': 'ཨི་གཟོ་འདི་ན་གས་ཁང་ར་ཡགཅིག་ཐོང་དགོས་ན་སུང་བ་ཐོང་ར་མིག་རིགས་དྲང་མྱོང་བ་ཁྲོད་ནོ་བས་མ་དག་ཏག་ཡིན'
        },
        {
            'name': 'With ▁ characters',
            'reference': 'བཀྲ་ཤིས▁བདེ་ལེགས',
            'predicted': 'བཀྲ ཤིས▁བདེ ལེགས'
        },
        {
            'name': 'Mixed tsheg and spaces',
            'reference': 'ཀྲུང་གོ འི་འཕེལ རྒྱས',
            'predicted': 'ཀྲུང གོ་འི འཕེལ་རྒྱས'
        },
        {
            'name': 'Perfect match after normalization',
            'reference': 'བཀྲ་ཤིས་བདེ་ལེགས',
            'predicted': 'བཀྲ ཤིས བདེ ལེགས'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {case['name']} ---")
        
        ref = case['reference']
        pred = case['predicted']
        
        print(f"Original Reference:  '{ref}'")
        print(f"Original Predicted:  '{pred}'")
        
        # Apply improved normalization
        ref_norm = normalize_tibetan_text(ref)
        pred_norm = normalize_tibetan_text(pred)
        
        print(f"\nNormalized Reference:  '{ref_norm}'")
        print(f"Normalized Predicted:  '{pred_norm}'")
        
        # Calculate metrics
        metrics = calculate_tibetan_metrics([ref], [pred])
        
        print(f"\nMetrics:")
        print(f"  CER: {metrics['cer']:.4f}")
        print(f"  WER: {metrics['wer']:.4f}")
        
        # Show character-by-character comparison for first few characters
        print(f"\nCharacter comparison (first 20 chars):")
        ref_chars = list(ref_norm[:20])
        pred_chars = list(pred_norm[:20])
        max_len = max(len(ref_chars), len(pred_chars))
        
        print("Ref: ", end="")
        for j in range(max_len):
            char = ref_chars[j] if j < len(ref_chars) else ' '
            print(f"{char:2}", end="")
        print()
        
        print("Pred:", end="")
        for j in range(max_len):
            char = pred_chars[j] if j < len(pred_chars) else ' '
            print(f"{char:2}", end="")
        print()
        
        print("Match:", end="")
        for j in range(max_len):
            ref_char = ref_chars[j] if j < len(ref_chars) else ''
            pred_char = pred_chars[j] if j < len(pred_chars) else ''
            match = '✓' if ref_char == pred_char else '✗'
            print(f"{match:2}", end="")
        print()
        
        print("-" * 70)

def test_normalization_steps():
    """Test each step of the normalization process"""
    print("\n" + "="*70)
    print("NORMALIZATION STEPS BREAKDOWN")
    print("="*70)
    
    test_text = "འུ་བཟོ▁འདི ན་ཟ ཁང་ར"
    print(f"Original text: '{test_text}'")
    
    # Step 1: Replace tsheg with spaces
    step1 = test_text.replace('་', ' ')
    print(f"Step 1 (tsheg->space): '{step1}'")
    
    # Step 2: Replace ▁ with spaces
    step2 = step1.replace('▁', ' ')
    print(f"Step 2 (▁->space): '{step2}'")
    
    # Step 3: Remove all spaces
    import re
    step3 = re.sub(r'\s+', '', step2)
    print(f"Step 3 (remove spaces): '{step3}'")
    
    # Step 4: BasicTextNormalizer
    from transformers.models.whisper.english_normalizer import BasicTextNormalizer
    normalizer = BasicTextNormalizer()
    step4 = normalizer(step3)
    print(f"Step 4 (BasicTextNormalizer): '{step4}'")
    
    # Compare with our function
    final_result = normalize_tibetan_text(test_text)
    print(f"Final result: '{final_result}'")
    print(f"Match: {step4 == final_result}")

if __name__ == "__main__":
    test_improved_normalization()
    test_normalization_steps()
    
    print("\n" + "="*70)
    print("USAGE RECOMMENDATION")
    print("="*70)
    print("The improved normalization method should give more accurate")
    print("WER/CER results for Tibetan text by:")
    print("1. Unifying different separator formats (tsheg vs space)")
    print("2. Handling subword tokens (▁)")
    print("3. Removing spacing inconsistencies")
    print("4. Applying standard text normalization")
    print("\nUsage:")
    print("python calculate_tibetan_metrics.py --ref ref.txt --hyp hyp.txt")
